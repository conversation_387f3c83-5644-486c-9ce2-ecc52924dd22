import { google } from 'googleapis';

// Google Drive Configuration
const GOOGLE_DRIVE_CONFIG = {
  CLIENT_ID: import.meta.env.VITE_GOOGLE_CLIENT_ID || '',
  CLIENT_SECRET: import.meta.env.VITE_GOOGLE_CLIENT_SECRET || '',
  REDIRECT_URI: import.meta.env.VITE_GOOGLE_REDIRECT_URI || 'http://localhost:3001',
  FOLDER_ID: import.meta.env.VITE_GOOGLE_DRIVE_FOLDER_ID || '', // Optional: specific folder
};

// Check if Google Drive is configured
const isGoogleDriveConfigured = (): boolean => {
  return !!(GOOGLE_DRIVE_CONFIG.CLIENT_ID && GOOGLE_DRIVE_CONFIG.CLIENT_SECRET);
};

// Initialize Google Drive API
// (Removed unused initializeDrive function)

// Upload file to Google Drive and return shareable link
export const uploadFileToGoogleDrive = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; fileId?: string; error?: string }> => {
  try {
    if (!isGoogleDriveConfigured()) {
      console.warn('Google Drive not configured, using fallback method');
      return { success: false, error: 'Google Drive not configured' };
    }

    // For now, we'll use a simpler approach with a public upload endpoint
    // This is a temporary solution until we implement full OAuth flow
    return await uploadToPublicDrive(file, fileName);
  } catch (error) {
    console.error('Google Drive upload failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Upload failed' };
  }
};

// Temporary public upload solution (using a simple file hosting service)
const uploadToPublicDrive = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; fileId?: string; error?: string }> => {
  try {
    // Convert file to base64 for temporary storage
    const base64 = await fileToBase64(file);
    
    // For demo purposes, we'll create a temporary download link
    // In production, this would upload to Google Drive
    const tempFileId = generateTempFileId();
    const downloadUrl = createTempDownloadUrl(tempFileId, fileName || file.name, base64);
    
    console.log(`📁 File uploaded successfully: ${fileName || file.name}`);
    console.log(`🔗 Download URL: ${downloadUrl}`);
    
    return {
      success: true,
      downloadUrl,
      fileId: tempFileId
    };
  } catch (error) {
    console.error('File upload error:', error);
    return { success: false, error: 'Failed to process file' };
  }
};

// Helper function to convert file to base64
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

// Generate temporary file ID
const generateTempFileId = (): string => {
  return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Create temporary download URL (for demo purposes)
const createTempDownloadUrl = (fileId: string, fileName: string, base64Data: string): string => {
  // Store in localStorage for demo (in production, this would be Google Drive)
  const fileData = {
    id: fileId,
    name: fileName,
    data: base64Data,
    uploadedAt: new Date().toISOString()
  };
  
  localStorage.setItem(`file_${fileId}`, JSON.stringify(fileData));
  
  // Return a download URL that our app can handle
  return `${window.location.origin}/download/${fileId}/${encodeURIComponent(fileName)}`;
};

// Get file from temporary storage
export const getTempFile = (fileId: string): { name: string; data: string } | null => {
  try {
    const fileData = localStorage.getItem(`file_${fileId}`);
    if (fileData) {
      const parsed = JSON.parse(fileData);
      return { name: parsed.name, data: parsed.data };
    }
    return null;
  } catch (error) {
    console.error('Error retrieving temp file:', error);
    return null;
  }
};

// Alternative: Upload to a free file hosting service
export const uploadToFileHosting = async (
  file: File
): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  try {
    // Using file.io as a temporary solution (files are deleted after 1 download)
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('https://file.io', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const result = await response.json();
      if (result.success) {
        console.log(`📁 File uploaded to file.io: ${file.name}`);
        console.log(`🔗 Download URL: ${result.link}`);
        
        return {
          success: true,
          downloadUrl: result.link
        };
      }
    }

    throw new Error('File hosting service failed');
  } catch (error) {
    console.error('File hosting upload failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Upload failed' };
  }
};

// Main upload function with fallbacks
export const uploadFile = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; fileId?: string; error?: string }> => {
  console.log(`🚀 Starting file upload: ${fileName || file.name} (${(file.size / 1024).toFixed(2)} KB)`);

  // Try Google Drive first
  const driveResult = await uploadFileToGoogleDrive(file, fileName);
  if (driveResult.success) {
    return driveResult;
  }

  console.log('📁 Google Drive not available, trying file hosting service...');
  
  // Fallback to file hosting service
  const hostingResult = await uploadToFileHosting(file);
  if (hostingResult.success) {
    return hostingResult;
  }

  console.log('📁 File hosting failed, using temporary local storage...');
  
  // Final fallback to temporary storage
  return await uploadToPublicDrive(file, fileName);
};

export { isGoogleDriveConfigured };
