import { google } from 'googleapis';

// Google Drive Configuration
const GOOGLE_DRIVE_CONFIG = {
  CLIENT_ID: import.meta.env.VITE_GOOGLE_CLIENT_ID || '',
  CLIENT_SECRET: import.meta.env.VITE_GOOGLE_CLIENT_SECRET || '',
  REDIRECT_URI: import.meta.env.VITE_GOOGLE_REDIRECT_URI || 'http://localhost:3001',
  FOLDER_ID: import.meta.env.VITE_GOOGLE_DRIVE_FOLDER_ID || '', // Optional: specific folder
};

// Check if Google Drive is configured
const isGoogleDriveConfigured = (): boolean => {
  return !!(GOOGLE_DRIVE_CONFIG.CLIENT_ID && GOOGLE_DRIVE_CONFIG.CLIENT_SECRET);
};

// Initialize Google Drive API
// (Removed unused initializeDrive function)

// Upload file to Google Drive and return shareable link
export const uploadFileToGoogleDrive = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; fileId?: string; error?: string }> => {
  try {
    if (!isGoogleDriveConfigured()) {
      console.warn('Google Drive not configured, using fallback method');
      return { success: false, error: 'Google Drive not configured' };
    }

    // For now, we'll use a simpler approach with a public upload endpoint
    // This is a temporary solution until we implement full OAuth flow
    return await uploadToPublicDrive(file, fileName);
  } catch (error) {
    console.error('Google Drive upload failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Upload failed' };
  }
};

// Temporary public upload solution (using a simple file hosting service)
const uploadToPublicDrive = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; fileId?: string; error?: string }> => {
  try {
    // Convert file to base64 for temporary storage
    const base64 = await fileToBase64(file);
    
    // For demo purposes, we'll create a temporary download link
    // In production, this would upload to Google Drive
    const tempFileId = generateTempFileId();
    const downloadUrl = createTempDownloadUrl(tempFileId, fileName || file.name, base64);
    
    console.log(`📁 File uploaded successfully: ${fileName || file.name}`);
    console.log(`🔗 Download URL: ${downloadUrl}`);
    
    return {
      success: true,
      downloadUrl,
      fileId: tempFileId
    };
  } catch (error) {
    console.error('File upload error:', error);
    return { success: false, error: 'Failed to process file' };
  }
};

// Helper function to convert file to base64
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

// Generate temporary file ID
const generateTempFileId = (): string => {
  return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Create temporary download URL (for demo purposes)
const createTempDownloadUrl = (fileId: string, fileName: string, base64Data: string): string => {
  // Store in localStorage for demo (in production, this would be Google Drive)
  const fileData = {
    id: fileId,
    name: fileName,
    data: base64Data,
    uploadedAt: new Date().toISOString()
  };
  
  localStorage.setItem(`file_${fileId}`, JSON.stringify(fileData));
  
  // Return a download URL that our app can handle
  return `${window.location.origin}/download/${fileId}/${encodeURIComponent(fileName)}`;
};

// Get file from temporary storage
export const getTempFile = (fileId: string): { name: string; data: string } | null => {
  try {
    const fileData = localStorage.getItem(`file_${fileId}`);
    if (fileData) {
      const parsed = JSON.parse(fileData);
      return { name: parsed.name, data: parsed.data };
    }
    return null;
  } catch (error) {
    console.error('Error retrieving temp file:', error);
    return null;
  }
};

// Alternative: Upload to a free file hosting service
export const uploadToFileHosting = async (
  file: File
): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  try {
    console.log(`📤 Uploading to file.io: ${file.name} (${(file.size / 1024).toFixed(2)} KB)`);

    // Using file.io as a temporary solution (files are deleted after 1 download)
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('https://file.io', {
      method: 'POST',
      body: formData,
      // Add headers to ensure proper handling
      headers: {
        'Accept': 'application/json',
      }
    });

    console.log(`📡 File.io response status: ${response.status}`);

    if (response.ok) {
      const result = await response.json();
      console.log('📡 File.io response:', result);

      if (result.success && result.link) {
        console.log(`✅ File uploaded to file.io: ${file.name}`);
        console.log(`🔗 Download URL: ${result.link}`);

        return {
          success: true,
          downloadUrl: result.link
        };
      } else {
        console.error('❌ File.io upload failed:', result);
        throw new Error(result.message || 'File.io upload failed');
      }
    } else {
      const errorText = await response.text();
      console.error('❌ File.io HTTP error:', response.status, errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
  } catch (error) {
    console.error('❌ File hosting upload failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Upload failed' };
  }
};

// Alternative file hosting service (0x0.st)
export const uploadTo0x0 = async (
  file: File
): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  try {
    console.log(`📤 Uploading to 0x0.st: ${file.name}`);

    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('https://0x0.st', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const downloadUrl = await response.text();
      if (downloadUrl && downloadUrl.startsWith('https://')) {
        console.log(`✅ File uploaded to 0x0.st: ${file.name}`);
        console.log(`🔗 Download URL: ${downloadUrl.trim()}`);

        return {
          success: true,
          downloadUrl: downloadUrl.trim()
        };
      }
    }

    throw new Error('0x0.st upload failed');
  } catch (error) {
    console.error('❌ 0x0.st upload failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Upload failed' };
  }
};

// Simple file hosting using tmpfiles.org
export const uploadToTmpFiles = async (
  file: File
): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  try {
    console.log(`📤 Uploading to tmpfiles.org: ${file.name}`);

    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('https://tmpfiles.org/api/v1/upload', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const result = await response.json();
      if (result.status === 'success' && result.data && result.data.url) {
        const downloadUrl = result.data.url.replace('tmpfiles.org/', 'tmpfiles.org/dl/');
        console.log(`✅ File uploaded to tmpfiles.org: ${file.name}`);
        console.log(`🔗 Download URL: ${downloadUrl}`);

        return {
          success: true,
          downloadUrl: downloadUrl
        };
      }
    }

    throw new Error('tmpfiles.org upload failed');
  } catch (error) {
    console.error('❌ tmpfiles.org upload failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Upload failed' };
  }
};

// Main upload function with fallbacks
export const uploadFile = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; fileId?: string; error?: string }> => {
  console.log(`🚀 Starting file upload: ${fileName || file.name} (${(file.size / 1024).toFixed(2)} KB)`);

  // Try Google Drive first (if configured)
  if (isGoogleDriveConfigured()) {
    console.log('📁 Trying Google Drive...');
    const driveResult = await uploadFileToGoogleDrive(file, fileName);
    if (driveResult.success) {
      return driveResult;
    }
    console.log('❌ Google Drive failed, trying other services...');
  } else {
    console.log('⚠️ Google Drive not configured, skipping...');
  }

  // Try file.io
  console.log('📁 Trying file.io...');
  const fileIoResult = await uploadToFileHosting(file);
  if (fileIoResult.success) {
    return fileIoResult;
  }
  console.log('❌ File.io failed, trying tmpfiles.org...');

  // Try tmpfiles.org
  console.log('📁 Trying tmpfiles.org...');
  const tmpResult = await uploadToTmpFiles(file);
  if (tmpResult.success) {
    return tmpResult;
  }
  console.log('❌ tmpfiles.org failed, trying 0x0.st...');

  // Try 0x0.st as backup
  console.log('📁 Trying 0x0.st...');
  const zeroResult = await uploadTo0x0(file);
  if (zeroResult.success) {
    return zeroResult;
  }
  console.log('❌ All external services failed, using local fallback...');

  // Final fallback to temporary storage (localhost - for demo only)
  console.log('📁 Using local storage fallback (demo only)...');
  return await uploadToPublicDrive(file, fileName);
};

// Test function to check file upload services
export const testFileUploadServices = async (): Promise<void> => {
  console.log('🧪 Testing file upload services...');

  // Create a small test file
  const testContent = 'This is a test file for Defendly file upload service.';
  const testFile = new File([testContent], 'test.txt', { type: 'text/plain' });

  console.log('📤 Testing with file:', testFile.name, `(${testFile.size} bytes)`);

  // Test file.io
  try {
    const result = await uploadToFileHosting(testFile);
    if (result.success) {
      console.log('✅ File.io test successful:', result.downloadUrl);
    } else {
      console.log('❌ File.io test failed:', result.error);
    }
  } catch (error) {
    console.log('❌ File.io test error:', error);
  }

  // Test tmpfiles.org
  try {
    const result = await uploadToTmpFiles(testFile);
    if (result.success) {
      console.log('✅ tmpfiles.org test successful:', result.downloadUrl);
    } else {
      console.log('❌ tmpfiles.org test failed:', result.error);
    }
  } catch (error) {
    console.log('❌ tmpfiles.org test error:', error);
  }

  // Test 0x0.st
  try {
    const result = await uploadTo0x0(testFile);
    if (result.success) {
      console.log('✅ 0x0.st test successful:', result.downloadUrl);
    } else {
      console.log('❌ 0x0.st test failed:', result.error);
    }
  } catch (error) {
    console.log('❌ 0x0.st test error:', error);
  }
};

export { isGoogleDriveConfigured };
