
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Globe, Apple as Api, Smartphone, Network, Cloud, ShieldCheck, Upload, AlertCircle, Mail, CheckCircle } from 'lucide-react';
import { sendAssessmentRequestEmail, AssessmentEmailData } from '../services/emailService';

interface FormData {
  organizationName: string;
  projectName: string;
  selectedType: string | null;
  webAppUrl?: string;
  apiEndpoints?: string;
  apiFile?: File;
  mobileAppLink?: string;
  mobileAppFile?: File;
  networkLink?: string;
  networkIp?: string;
  cloudLink?: string;
  cloudPlatform?: string;
  cloudDescription?: string;
  complianceLink?: string;
  complianceOrgs?: number;
  complianceType?: string;
}

const AdvancedTesting: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    organizationName: '',
    projectName: '',
    selectedType: null
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const assessmentTypes = [
    {
      id: 'web',
      title: 'Web Application',
      icon: Globe,
      description: 'Comprehensive security assessment of web applications'
    },
    {
      id: 'api',
      title: 'API Testing',
      icon: Api,
      description: 'Security testing of REST, GraphQL, and SOAP APIs'
    },
    {
      id: 'mobile',
      title: 'Mobile App',
      icon: Smartphone,
      description: 'Android and iOS application security assessment'
    },
    {
      id: 'network',
      title: 'Network Infrastructure',
      icon: Network,
      description: 'Network security and penetration testing'
    },
    {
      id: 'cloud',
      title: 'Cloud Security',
      icon: Cloud,
      description: 'Cloud infrastructure and configuration review'
    },
    {
      id: 'compliance',
      title: 'Compliance Assessment',
      icon: ShieldCheck,
      description: 'Regulatory compliance and security standards'
    }
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.organizationName) {
      newErrors.organizationName = 'Organization name is required';
    }
    if (!formData.projectName) {
      newErrors.projectName = 'Project name is required';
    }
    if (!formData.selectedType) {
      newErrors.selectedType = 'Please select an assessment type';
    }

    // Validate type-specific fields
    switch (formData.selectedType) {
      case 'web':
        if (!formData.webAppUrl) {
          newErrors.webAppUrl = 'Web application URL is required';
        }
        break;
      case 'api':
        if (!formData.apiEndpoints && !formData.apiFile) {
          newErrors.apiEndpoints = 'Either endpoints or JSON file is required';
        }
        break;
      case 'mobile':
        if (!formData.mobileAppLink && !formData.mobileAppFile) {
          newErrors.mobileAppLink = 'Either app link or APK file is required';
        }
        break;
      case 'network':
        if (!formData.networkLink && !formData.networkIp) {
          newErrors.networkLink = 'Either link or IP is required';
        }
        break;
      case 'cloud':
        if (!formData.cloudLink) {
          newErrors.cloudLink = 'Cloud resource link is required';
        }
        if (!formData.cloudPlatform) {
          newErrors.cloudPlatform = 'Cloud platform selection is required';
        }
        break;
      case 'compliance':
        if (!formData.complianceLink) {
          newErrors.complianceLink = 'Resource link is required';
        }
        if (!formData.complianceType) {
          newErrors.complianceType = 'Compliance type selection is required';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Email service function
  const sendAssessmentEmail = async (assessmentData: FormData) => {
    try {
      const selectedAssessment = assessmentTypes.find(t => t.id === assessmentData.selectedType);

      if (!selectedAssessment) {
        throw new Error('Assessment type not found');
      }

      // Prepare assessment details based on type
      const details: Record<string, string | number | undefined> = {};

      switch (assessmentData.selectedType) {
        case 'web':
          details.webApplicationUrl = assessmentData.webAppUrl;
          break;
        case 'api':
          details.apiEndpoints = assessmentData.apiEndpoints;
          if (assessmentData.apiFile) {
            details.apiFileName = assessmentData.apiFile.name;
          }
          break;
        case 'mobile':
          details.mobileAppLink = assessmentData.mobileAppLink;
          if (assessmentData.mobileAppFile) {
            details.mobileAppFileName = assessmentData.mobileAppFile.name;
          }
          break;
        case 'network':
          details.networkLink = assessmentData.networkLink;
          details.ipAddress = assessmentData.networkIp;
          break;
        case 'cloud':
          details.cloudResourceLink = assessmentData.cloudLink;
          details.cloudPlatform = assessmentData.cloudPlatform;
          details.cloudDescription = assessmentData.cloudDescription;
          break;
        case 'compliance':
          details.resourceLink = assessmentData.complianceLink;
          details.numberOfOrganizations = assessmentData.complianceOrgs;
          details.complianceType = assessmentData.complianceType;
          break;
      }

      const emailData: AssessmentEmailData = {
        organizationName: assessmentData.organizationName,
        projectName: assessmentData.projectName,
        assessmentType: selectedAssessment.title,
        assessmentDescription: selectedAssessment.description,
        details,
        submittedAt: new Date().toLocaleString()
      };

      // Send email using the email service
      return await sendAssessmentRequestEmail(emailData);
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      setIsSubmitting(true);

      try {
        // Send email with assessment details
        const emailSent = await sendAssessmentEmail(formData);

        if (emailSent) {
          setShowSuccess(true);
          // Auto-hide success message after 3 seconds
          setTimeout(() => {
            setShowSuccess(false);
            navigate('/assessment');
          }, 3000);
        } else {
          // Still navigate but show a different message
          console.log('Form submitted but email failed:', formData);
          navigate('/assessment');
        }
      } catch (error) {
        console.error('Submission error:', error);
        // Still navigate on error
        navigate('/assessment');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, field: keyof FormData) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, [field]: file }));
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const renderTypeSpecificFields = () => {
    switch (formData.selectedType) {
      case 'web':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Web Application URL
              </label>
              <input
                type="url"
                value={formData.webAppUrl || ''}
                onChange={(e) => setFormData({ ...formData, webAppUrl: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="https://example.com"
              />
              {errors.webAppUrl && (
                <p className="mt-1 text-xs text-danger-600 dark:text-danger-400">{errors.webAppUrl}</p>
              )}
            </div>
          </div>
        );

      case 'api':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                API Endpoints
              </label>
              <textarea
                value={formData.apiEndpoints || ''}
                onChange={(e) => setFormData({ ...formData, apiEndpoints: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="Enter API endpoints (one per line)"
                rows={4}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Upload API Specification (JSON)
              </label>
              <div className="flex items-center justify-center w-full">
                <label className="w-full flex flex-col items-center px-4 py-6 bg-white dark:bg-black text-neutral-700 dark:text-neutral-200 rounded-lg border-2 border-neutral-300 dark:border-neutral-700 border-dashed cursor-pointer hover:border-[#00457F] dark:hover:border-primary-400">
                  <Upload className="w-8 h-8 text-neutral-500 dark:text-neutral-400 mb-2" />
                  <span className="text-sm">Click to upload API specification</span>
                  <input
                    type="file"
                    className="hidden"
                    accept=".json"
                    onChange={(e) => handleFileChange(e, 'apiFile')}
                  />
                </label>
              </div>
              {formData.apiFile && (
                <p className="mt-2 text-sm text-neutral-600 dark:text-neutral-300">
                  Selected file: {formData.apiFile.name}
                </p>
              )}
            </div>
          </div>
        );

      case 'mobile':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Mobile App Link
              </label>
              <input
                type="url"
                value={formData.mobileAppLink || ''}
                onChange={(e) => setFormData({ ...formData, mobileAppLink: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="App Store or Play Store URL"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Upload APK File
              </label>
              <div className="flex items-center justify-center w-full">
                <label className="w-full flex flex-col items-center px-4 py-6 bg-white dark:bg-black text-neutral-700 dark:text-neutral-200 rounded-lg border-2 border-neutral-300 dark:border-neutral-700 border-dashed cursor-pointer hover:border-[#00457F] dark:hover:border-primary-400">
                  <Upload className="w-8 h-8 text-neutral-500 dark:text-neutral-400 mb-2" />
                  <span className="text-sm">Click to upload APK file</span>
                  <input
                    type="file"
                    className="hidden"
                    accept=".apk"
                    onChange={(e) => handleFileChange(e, 'mobileAppFile')}
                  />
                </label>
              </div>
              {formData.mobileAppFile && (
                <p className="mt-2 text-sm text-neutral-600 dark:text-neutral-300">
                  Selected file: {formData.mobileAppFile.name}
                </p>
              )}
            </div>
          </div>
        );

      case 'network':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Network Link
              </label>
              <input
                type="url"
                value={formData.networkLink || ''}
                onChange={(e) => setFormData({ ...formData, networkLink: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="https://example.com"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                IP Address
              </label>
              <input
                type="text"
                value={formData.networkIp || ''}
                onChange={(e) => setFormData({ ...formData, networkIp: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="***********"
              />
            </div>
          </div>
        );

      case 'cloud':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Cloud Resource Link
              </label>
              <input
                type="url"
                value={formData.cloudLink || ''}
                onChange={(e) => setFormData({ ...formData, cloudLink: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="https://example.cloud.com"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Cloud Platform
              </label>
              <select
                value={formData.cloudPlatform || ''}
                onChange={(e) => setFormData({ ...formData, cloudPlatform: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
              >
                <option value="">Select Platform</option>
                <option value="aws">AWS</option>
                <option value="azure">Azure</option>
                <option value="gcp">Google Cloud Platform</option>
                <option value="Others">Others</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Description
              </label>
              <textarea
                value={formData.cloudDescription || ''}
                onChange={(e) => setFormData({ ...formData, cloudDescription: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="Describe your cloud infrastructure"
                rows={4}
              />
            </div>
          </div>
        );

      case 'compliance':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Resource Link
              </label>
              <input
                type="url"
                value={formData.complianceLink || ''}
                onChange={(e) => setFormData({ ...formData, complianceLink: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="https://example.com"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Number of Organizations
              </label>
              <input
                type="number"
                value={formData.complianceOrgs || ''}
                onChange={(e) => setFormData({ ...formData, complianceOrgs: parseInt(e.target.value) })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                min="1"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Compliance Type
              </label>
              <select
                value={formData.complianceType || ''}
                onChange={(e) => setFormData({ ...formData, complianceType: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
              >
                <option value="">Select Type</option>
                <option value="iso27k">ISO 27001</option>
                <option value="gdpr">GDPR</option>
                <option value="soc">SOC 1/2/3</option>
                <option value="hipaa">HIPAA</option>
                <option value="pcidss">PCI DSS</option>
                <option value="ccss">CCSS</option>
                <option value="Others">Others</option>
              </select>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="px-4 py-6">
      <button
        onClick={() => navigate('/assessment')}
        className="flex items-center text-neutral-400 hover:text-neutral-600 dark:text-neutral-300 dark:hover:text-neutral-100 transition-colors mb-4"
      >
        <ArrowLeft className="w-4 h-4 mr-1" />
        <span>Back</span>
      </button>

      <div className="mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">Types of Assessments</h1>
        <p className="text-neutral-600 dark:text-neutral-300">
          Select the type of security assessment that best fits your needs
        </p>
      </div>

      {/* Success Message */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
          >
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <div>
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                  Assessment Request Submitted Successfully!
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                  <Mail className="w-4 h-4 inline mr-1" />
                  Details have been sent via email. You will be redirected shortly.
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div className="max-w-4xl">
        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Organization Name
              </label>
              <input
                type="text"
                value={formData.organizationName}
                onChange={(e) => setFormData({ ...formData, organizationName: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="Enter organization name"
              />
              {errors.organizationName && (
                <p className="mt-1 text-xs text-danger-600 dark:text-danger-400">{errors.organizationName}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                Project Name
              </label>
              <input
                type="text"
                value={formData.projectName}
                onChange={(e) => setFormData({ ...formData, projectName: e.target.value })}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md   bg-white dark:bg-black text-neutral-900 dark:text-neutral-100"
                placeholder="Enter project name"
              />
              {errors.projectName && (
                <p className="mt-1 text-xs text-danger-600 dark:text-danger-400">{errors.projectName}</p>
              )}
            </div>
          </div>

          <div>
            <h2 className="text-lg font-semibold mb-4 dark:text-neutral-100">Select Assessment Type</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {assessmentTypes.map((type) => (
                <motion.div
                  key={type.id}
                  whileHover={{ y: -2 }}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                    formData.selectedType === type.id
                      ? 'border-[#00457F] bg-primary-50 dark:bg-[#00457F]'
                      : 'border-neutral-200 dark:border-neutral-700 hover:border-[#00457F] dark:hover:border-primary-400 bg-white dark:bg-black'
                  }`}
                  onClick={() => setFormData({ ...formData, selectedType: type.id })}
                >
                  <div className="flex items-start">
                    <div className={`p-2 rounded-lg ${
                      formData.selectedType === type.id
                        ? 'bg-primary-100 dark:bg-white'
                        : 'bg-neutral-100 dark:bg-neutral-800'
                    }`}>
                      <type.icon className={`w-5 h-5 ${
                        formData.selectedType === type.id
                          ? 'text-primary-600 dark:text-[#00457F]'
                          : 'text-neutral-600 dark:text-neutral-200'
                      }`} />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                        {type.title}
                      </h3>
                      <p className="mt-1 text-xs text-neutral-500 dark:text-neutral-300">
                        {type.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
            {errors.selectedType && (
              <p className="mt-2 text-xs text-danger-600 dark:text-danger-400">{errors.selectedType}</p>
            )}
          </div>

          <AnimatePresence mode="wait">
            {formData.selectedType && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
                className="bg-white dark:bg-black rounded-lg border border-neutral-200 dark:border-neutral-700 p-6"
              >
                <h3 className="text-lg font-semibold mb-4 dark:text-neutral-100">
                  {assessmentTypes.find(t => t.id === formData.selectedType)?.title} Details
                </h3>
                {renderTypeSpecificFields()}
              </motion.div>
            )}
          </AnimatePresence>

          <div className="flex justify-start">
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2 bg-[#00457F] text-white rounded-md font-medium hover:bg-[#00457F]/50 focus:outline-none focus:ring-1 focus:ring-[#00457F] focus:ring-offset-1 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Submitting...
                </>
              ) : (
                <>
                  <Mail className="w-4 h-4 mr-2" />
                  Submit Your Scan Request
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdvancedTesting;