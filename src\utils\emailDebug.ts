/**
 * Email debugging utilities for development
 */

export interface MockEmail {
  id: string;
  to: string;
  subject: string;
  html: string;
  from?: string;
  timestamp: string;
}

/**
 * Get all mock emails from localStorage
 */
export const getMockEmails = (): MockEmail[] => {
  try {
    const emails = localStorage.getItem('mockEmails');
    return emails ? JSON.parse(emails) : [];
  } catch (error) {
    console.error('Failed to get mock emails:', error);
    return [];
  }
};

/**
 * Clear all mock emails from localStorage
 */
export const clearMockEmails = (): void => {
  try {
    localStorage.removeItem('mockEmails');
    console.log('Mock emails cleared');
  } catch (error) {
    console.error('Failed to clear mock emails:', error);
  }
};

/**
 * Get the latest mock email
 */
export const getLatestMockEmail = (): MockEmail | null => {
  const emails = getMockEmails();
  return emails.length > 0 ? emails[emails.length - 1] : null;
};

/**
 * Log all mock emails to console (for debugging)
 */
export const logMockEmails = (): void => {
  const emails = getMockEmails();
  console.log('📧 Mock Emails:', emails);
  
  if (emails.length === 0) {
    console.log('No mock emails found');
    return;
  }
  
  emails.forEach((email, index) => {
    console.log(`\n--- Email ${index + 1} ---`);
    console.log('ID:', email.id);
    console.log('To:', email.to);
    console.log('Subject:', email.subject);
    console.log('Timestamp:', email.timestamp);
    console.log('HTML Preview:', email.html.substring(0, 200) + '...');
  });
};

/**
 * Add debug methods to window object for easy console access
 */
if (typeof window !== 'undefined') {
  (window as any).emailDebug = {
    getMockEmails,
    clearMockEmails,
    getLatestMockEmail,
    logMockEmails
  };
  
  console.log('📧 Email debug utilities available at window.emailDebug');
  console.log('Available methods: getMockEmails(), clearMockEmails(), getLatestMockEmail(), logMockEmails()');
}
