import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getTempFile } from '../services/googleDriveService';

const FileDownloadHandler: React.FC = () => {
  const { fileId, fileName } = useParams<{ fileId: string; fileName: string }>();
  const navigate = useNavigate();

  useEffect(() => {
    if (fileId) {
      handleFileDownload(fileId, fileName || 'download');
    }
  }, [fileId, fileName]);

  const handleFileDownload = (id: string, name: string) => {
    try {
      const fileData = getTempFile(id);
      
      if (!fileData) {
        alert('File not found or has expired');
        navigate('/');
        return;
      }

      // Convert base64 to blob and trigger download
      const base64Data = fileData.data.split(',')[1]; // Remove data:type;base64, prefix
      const byteCharacters = atob(base64Data);
      const byteNumbers = new Array(byteCharacters.length);
      
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray]);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = decodeURIComponent(name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      // Redirect back to home after download
      setTimeout(() => {
        navigate('/');
      }, 1000);
      
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
      navigate('/');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-neutral-50 dark:bg-neutral-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-2">
          Preparing Download...
        </h2>
        <p className="text-neutral-600 dark:text-neutral-400">
          Your file download will start shortly
        </p>
      </div>
    </div>
  );
};

export default FileDownloadHandler;
