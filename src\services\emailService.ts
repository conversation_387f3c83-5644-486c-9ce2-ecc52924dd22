/**
 * Email Service for sending assessment requests and notifications
 */
import { apiPost } from '../utils/api';
import emailjs from '@emailjs/browser';
import { ENV_CONFIG } from '../utils/envConfig';

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

export interface AssessmentEmailData {
  organizationName: string;
  projectName: string;
  assessmentType: string;
  assessmentDescription: string;
  details: Record<string, string | number | undefined>;
  submittedAt: string;
}

// EmailJS Configuration from environment variables
const EMAILJS_CONFIG = {
  SERVICE_ID: ENV_CONFIG.EMAILJS_SERVICE_ID || 'service_defendly',
  TEMPLATE_ID: ENV_CONFIG.EMAILJS_TEMPLATE_ID || 'template_assessment',
  PUBLIC_KEY: ENV_CONFIG.EMAILJS_PUBLIC_KEY || 'YOUR_EMAILJS_PUBLIC_KEY'
};

/**
 * Send email via multiple services with fallbacks
 */
export const sendEmail = async (emailData: EmailData): Promise<boolean> => {
  // Try EmailJS first
  try {
    const result = await sendEmailViaEmailJS(emailData);
    return result;
  } catch (emailjsError) {
    console.warn('EmailJS not configured, trying Web3Forms:', emailjsError);
  }

  // Try Web3Forms as alternative
  try {
    const result = await sendEmailViaWeb3Forms(emailData);
    return result;
  } catch (web3Error) {
    console.warn('Web3Forms failed, trying backend API:', web3Error);
  }

  // Try backend API
  try {
    await apiPost('/api/send-email', emailData);
    console.log('✅ Email sent successfully via Backend API');
    return true;
  } catch (backendError) {
    console.warn('Backend email API not available, using mock email service:', backendError);
  }

  // Final fallback to mock email service for development
  console.log('📧 Using mock email service for development');
  return mockEmailService(emailData);
};

/**
 * Send email using EmailJS service
 */
const sendEmailViaEmailJS = async (emailData: EmailData): Promise<boolean> => {
  try {
    // Check if EmailJS is configured
    if (EMAILJS_CONFIG.PUBLIC_KEY === 'YOUR_EMAILJS_PUBLIC_KEY') {
      throw new Error('EmailJS not configured');
    }

    // Initialize EmailJS
    emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY);

    // Prepare template parameters
    const templateParams = {
      to_email: emailData.to,
      from_email: emailData.from || '<EMAIL>',
      subject: emailData.subject,
      message_html: emailData.html,
      reply_to: '<EMAIL>'
    };

    // Send email via EmailJS
    const response = await emailjs.send(
      EMAILJS_CONFIG.SERVICE_ID,
      EMAILJS_CONFIG.TEMPLATE_ID,
      templateParams
    );

    console.log('✅ Email sent successfully via EmailJS:', response);
    return true;
  } catch (error) {
    console.error('❌ Failed to send email via EmailJS:', error);
    throw error;
  }
};

/**
 * Send email using Web3Forms service (free alternative)
 */
const sendEmailViaWeb3Forms = async (emailData: EmailData): Promise<boolean> => {
  try {
    // Check if Web3Forms is configured
    const accessKey = ENV_CONFIG.WEB3FORMS_ACCESS_KEY;
    if (!accessKey || accessKey === 'YOUR_WEB3FORMS_KEY') {
      throw new Error('Web3Forms not configured');
    }

    const formData = new FormData();
    formData.append('access_key', accessKey);
    formData.append('subject', emailData.subject);
    formData.append('email', emailData.to);
    formData.append('message', emailData.html);
    formData.append('from_name', 'Defendly Security Platform');

    const response = await fetch('https://api.web3forms.com/submit', {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Web3Forms API error: ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      console.log('✅ Email sent successfully via Web3Forms');
      return true;
    } else {
      throw new Error('Web3Forms submission failed');
    }
  } catch (error) {
    console.error('❌ Failed to send email via Web3Forms:', error);
    throw error;
  }
};

/**
 * Mock email service for development/testing
 */
const mockEmailService = async (emailData: EmailData): Promise<boolean> => {
  return new Promise((resolve) => {
    // Simulate API delay
    setTimeout(() => {
      console.log('📧 Mock Email Sent Successfully!');
      console.log('To:', emailData.to);
      console.log('Subject:', emailData.subject);
      console.log('HTML Content:', emailData.html);

      // Store email in localStorage for debugging
      const emails = JSON.parse(localStorage.getItem('mockEmails') || '[]');
      emails.push({
        ...emailData,
        timestamp: new Date().toISOString(),
        id: Date.now().toString()
      });
      localStorage.setItem('mockEmails', JSON.stringify(emails));

      resolve(true);
    }, 1000); // 1 second delay to simulate real API
  });
};

/**
 * Send assessment request email
 */
export const sendAssessmentRequestEmail = async (
  assessmentData: AssessmentEmailData,
  recipientEmail: string = '<EMAIL>'
): Promise<boolean> => {
  try {
    const emailData: EmailData = {
      to: recipientEmail,
      subject: `New Security Assessment Request - ${assessmentData.assessmentType}`,
      html: generateAssessmentEmailHTML(assessmentData)
    };

    return await sendEmail(emailData);
  } catch (error) {
    console.error('Failed to send assessment request email:', error);
    return false;
  }
};

/**
 * Generate HTML content for assessment email
 */
const generateAssessmentEmailHTML = (data: AssessmentEmailData): string => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Security Assessment Request</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #00457F; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { background-color: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
        .detail-row { margin: 10px 0; }
        .label { font-weight: bold; color: #00457F; }
        .value { margin-left: 10px; }
        .footer { margin-top: 20px; padding: 15px; background-color: #e9ecef; border-radius: 8px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🛡️ New Security Assessment Request</h1>
        </div>
        <div class="content">
          <div class="detail-row">
            <span class="label">Organization:</span>
            <span class="value">${data.organizationName}</span>
          </div>
          <div class="detail-row">
            <span class="label">Project:</span>
            <span class="value">${data.projectName}</span>
          </div>
          <div class="detail-row">
            <span class="label">Assessment Type:</span>
            <span class="value">${data.assessmentType}</span>
          </div>
          <div class="detail-row">
            <span class="label">Description:</span>
            <span class="value">${data.assessmentDescription}</span>
          </div>
          
          <h3>Assessment Details:</h3>
          ${generateDetailsHTML(data.details)}
          
          <div class="footer">
            <p><strong>Submitted at:</strong> ${data.submittedAt}</p>
            <p><em>This is an automated email from Defendly Security Assessment Platform.</em></p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Generate HTML for assessment details based on type
 */
const generateDetailsHTML = (details: Record<string, string | number | undefined>): string => {
  let html = '';
  
  for (const [key, value] of Object.entries(details)) {
    if (value && value !== '') {
      const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      html += `
        <div class="detail-row">
          <span class="label">${label}:</span>
          <span class="value">${value}</span>
        </div>
      `;
    }
  }
  
  return html || '<p>No additional details provided.</p>';
};

/**
 * Send notification email to user
 */
export const sendUserNotificationEmail = async (
  userEmail: string,
  assessmentType: string,
  organizationName: string
): Promise<boolean> => {
  try {
    const emailData: EmailData = {
      to: userEmail,
      subject: `Assessment Request Received - ${assessmentType}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Assessment Request Confirmation</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #00457F; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
            .content { background-color: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>✅ Assessment Request Confirmed</h1>
            </div>
            <div class="content">
              <p>Dear ${organizationName} Team,</p>
              <p>We have successfully received your <strong>${assessmentType}</strong> security assessment request.</p>
              <p>Our security experts will review your request and contact you within 24-48 hours with next steps.</p>
              <p>Thank you for choosing Defendly for your security assessment needs.</p>
              <br>
              <p>Best regards,<br>The Defendly Security Team</p>
            </div>
          </div>
        </body>
        </html>
      `
    };

    return await sendEmail(emailData);
  } catch (error) {
    console.error('Failed to send user notification email:', error);
    return false;
  }
};
