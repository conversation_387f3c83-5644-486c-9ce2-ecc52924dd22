/**
 * Email Service for sending assessment requests and notifications
 */
import { apiPost } from "../utils/api";
import emailjs from "@emailjs/browser";
import { ENV_CONFIG } from "../utils/envConfig";

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  from?: string;
  plainText?: string;
}

export interface AssessmentEmailData {
  organizationName: string;
  projectName: string;
  assessmentType: string;
  assessmentDescription: string;
  details: Record<string, string | number | undefined>;
  submittedAt: string;
}

// EmailJS Configuration from environment variables
const EMAILJS_CONFIG = {
  SERVICE_ID: ENV_CONFIG.EMAILJS_SERVICE_ID || "service_defendly",
  TEMPLATE_ID: ENV_CONFIG.EMAILJS_TEMPLATE_ID || "template_assessment",
  PUBLIC_KEY: ENV_CONFIG.EMAILJS_PUBLIC_KEY || "YOUR_EMAILJS_PUBLIC_KEY",
};

// Check if EmailJS is properly configured
const isEmailJSConfigured = (): boolean => {
  return !!(
    EMAILJS_CONFIG.PUBLIC_KEY &&
    EMAILJS_CONFIG.PUBLIC_KEY !== "YOUR_EMAILJS_PUBLIC_KEY" &&
    EMAILJS_CONFIG.SERVICE_ID &&
    EMAILJS_CONFIG.TEMPLATE_ID
  );
};

/**
 * Send email via multiple services with fallbacks
 */
export const sendEmail = async (emailData: EmailData): Promise<boolean> => {
  console.log("🚀 Starting email send process...");

  // Try EmailJS first (supports beautiful HTML emails)
  if (isEmailJSConfigured()) {
    try {
      console.log("📧 Attempting to send via EmailJS (HTML email)...");
      const result = await sendEmailViaEmailJS(emailData);
      return result;
    } catch (emailjsError) {
      console.warn("❌ EmailJS failed:", emailjsError);
    }
  } else {
    console.log("⚠️ EmailJS not configured - skipping HTML email option");
  }

  // Try Web3Forms as alternative (plain text only)
  try {
    console.log("📧 Attempting to send via Web3Forms (plain text)...");
    const result = await sendEmailViaWeb3Forms(emailData);
    return result;
  } catch (web3Error) {
    console.warn("❌ Web3Forms failed:", web3Error);
  }

  // Try backend API
  try {
    console.log("📧 Attempting to send via Backend API...");
    await apiPost("/api/send-email", emailData);
    console.log("✅ Email sent successfully via Backend API");
    return true;
  } catch (backendError) {
    console.warn("❌ Backend email API not available:", backendError);
  }

  // Final fallback to mock email service for development
  console.log("📧 Using mock email service for development");
  return mockEmailService(emailData);
};

/**
 * Send email using EmailJS service
 */
const sendEmailViaEmailJS = async (emailData: EmailData): Promise<boolean> => {
  try {
    // Check if EmailJS is configured
    if (!isEmailJSConfigured()) {
      throw new Error(
        "EmailJS not configured - missing service ID, template ID, or public key"
      );
    }

    // Initialize EmailJS
    emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY);

    // Prepare template parameters
    const templateParams = {
      to_email: emailData.to,
      from_email: emailData.from || "<EMAIL>",
      subject: emailData.subject,
      message_html: emailData.html,
      reply_to: "<EMAIL>",
    };

    // Send email via EmailJS
    const response = await emailjs.send(
      EMAILJS_CONFIG.SERVICE_ID,
      EMAILJS_CONFIG.TEMPLATE_ID,
      templateParams
    );

    console.log("✅ Email sent successfully via EmailJS:", response);
    return true;
  } catch (error) {
    console.error("❌ Failed to send email via EmailJS:", error);
    throw error;
  }
};

/**
 * Convert HTML to plain text for better email compatibility
 */
const convertHtmlToPlainText = (html: string): string => {
  // Remove HTML tags and convert to plain text
  const text = html
    .replace(/<style[^>]*>.*?<\/style>/gi, "") // Remove style tags
    .replace(/<script[^>]*>.*?<\/script>/gi, "") // Remove script tags
    .replace(/<[^>]+>/g, "") // Remove all HTML tags
    .replace(/&nbsp;/g, " ") // Replace &nbsp; with space
    .replace(/&amp;/g, "&") // Replace &amp; with &
    .replace(/&lt;/g, "<") // Replace &lt; with <
    .replace(/&gt;/g, ">") // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .replace(/&#39;/g, "'") // Replace &#39; with '
    .replace(/\s+/g, " ") // Replace multiple spaces with single space
    .trim(); // Remove leading/trailing whitespace

  return text;
};

/**
 * Send email using Web3Forms service (free alternative)
 */
const sendEmailViaWeb3Forms = async (
  emailData: EmailData
): Promise<boolean> => {
  try {
    // Check if Web3Forms is configured
    const accessKey = ENV_CONFIG.WEB3FORMS_ACCESS_KEY;
    if (!accessKey || accessKey === "YOUR_WEB3FORMS_KEY") {
      throw new Error("Web3Forms not configured");
    }

    // Use plain text version if available, otherwise convert HTML to plain text
    const plainTextMessage =
      emailData.plainText || convertHtmlToPlainText(emailData.html);

    const formData = new FormData();
    formData.append("access_key", accessKey);
    formData.append("subject", emailData.subject);
    formData.append("email", emailData.to);
    formData.append("message", plainTextMessage);
    formData.append("from_name", "Defendly Security Platform");
    formData.append("replyto", emailData.from || "<EMAIL>");

    const response = await fetch("https://api.web3forms.com/submit", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Web3Forms API error: ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      console.log("✅ Email sent successfully via Web3Forms");
      return true;
    } else {
      throw new Error("Web3Forms submission failed");
    }
  } catch (error) {
    console.error("❌ Failed to send email via Web3Forms:", error);
    throw error;
  }
};

/**
 * Mock email service for development/testing
 */
const mockEmailService = async (emailData: EmailData): Promise<boolean> => {
  return new Promise((resolve) => {
    // Simulate API delay
    setTimeout(() => {
      console.log("📧 Mock Email Sent Successfully!");
      console.log("To:", emailData.to);
      console.log("Subject:", emailData.subject);
      console.log("HTML Content:", emailData.html);

      // Store email in localStorage for debugging
      const emails = JSON.parse(localStorage.getItem("mockEmails") || "[]");
      emails.push({
        ...emailData,
        timestamp: new Date().toISOString(),
        id: Date.now().toString(),
      });
      localStorage.setItem("mockEmails", JSON.stringify(emails));

      resolve(true);
    }, 1000); // 1 second delay to simulate real API
  });
};

/**
 * Send assessment request email
 */
export const sendAssessmentRequestEmail = async (
  assessmentData: AssessmentEmailData,
  recipientEmail: string = "<EMAIL>"
): Promise<boolean> => {
  try {
    const emailData: EmailData = {
      to: recipientEmail,
      subject: `New Security Assessment Request - ${assessmentData.assessmentType}`,
      html: generateAssessmentEmailHTML(assessmentData),
      // Add plain text version for better compatibility
      plainText: generatePlainTextEmail(assessmentData),
    };

    return await sendEmail(emailData);
  } catch (error) {
    console.error("Failed to send assessment request email:", error);
    return false;
  }
};

/**
 * Generate plain text email content for Web3Forms
 */
const generatePlainTextEmail = (data: AssessmentEmailData): string => {
  let content = `🛡️ NEW SECURITY ASSESSMENT REQUEST\n\n`;
  content += `Organization: ${data.organizationName}\n`;
  content += `Project: ${data.projectName}\n`;
  content += `Assessment Type: ${data.assessmentType}\n`;
  content += `Description: ${data.assessmentDescription}\n\n`;

  content += `ASSESSMENT DETAILS:\n`;
  content += `${"-".repeat(40)}\n`;

  for (const [key, value] of Object.entries(data.details)) {
    if (value && value !== "") {
      const label = key
        .replace(/([A-Z])/g, " $1")
        .replace(/^./, (str) => str.toUpperCase());

      // Special handling for file content in plain text
      if (key.includes('FileContent')) {
        const fileName = data.details[key.replace('Content', 'Name')] || 'uploaded-file';
        content += `📎 File Attached: ${fileName}\n`;
        content += `   File content: [Base64 data - ${(value.toString().length / 1024).toFixed(2)} KB]\n`;
      } else if (key.includes('FileName')) {
        // Skip filename if we already handled file content
        if (!data.details[key.replace('Name', 'Content')]) {
          content += `📎 ${label}: ${value}\n`;
        }
      } else {
        content += `${label}: ${value}\n`;
      }
    }
  }

  content += `\nSubmitted at: ${data.submittedAt}\n\n`;
  content += `This email was sent from Defendly Security Assessment Platform.\n`;
  content += `Please review the assessment request and contact the organization for next steps.`;

  return content;
};

/**
 * Generate beautiful HTML content for assessment email with modern styling
 */
const generateAssessmentEmailHTML = (data: AssessmentEmailData): string => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Security Assessment Request</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 20px;
        }
        .email-container {
          max-width: 650px;
          margin: 0 auto;
          background: white;
          border-radius: 16px;
          overflow: hidden;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
          background: linear-gradient(135deg, #00457F 0%, #0066cc 100%);
          color: white;
          padding: 40px 30px;
          text-align: center;
          position: relative;
        }
        .header::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
          opacity: 0.3;
        }
        .header h1 {
          font-size: 28px;
          margin-bottom: 10px;
          position: relative;
          z-index: 1;
        }
        .header .subtitle {
          font-size: 16px;
          opacity: 0.9;
          position: relative;
          z-index: 1;
        }
        .content {
          padding: 40px 30px;
          background: white;
        }
        .info-grid {
          display: grid;
          gap: 20px;
          margin-bottom: 30px;
        }
        .info-card {
          background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
          border-left: 4px solid #00457F;
          padding: 20px;
          border-radius: 8px;
          transition: transform 0.2s ease;
        }
        .info-card:hover {
          transform: translateY(-2px);
        }
        .info-label {
          font-weight: 600;
          color: #00457F;
          font-size: 14px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 5px;
        }
        .info-value {
          font-size: 16px;
          color: #333;
          font-weight: 500;
        }
        .details-section {
          background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
          border-radius: 12px;
          padding: 25px;
          margin: 25px 0;
          border: 1px solid #ffd6d6;
        }
        .details-title {
          font-size: 20px;
          font-weight: 700;
          color: #d63384;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
        }
        .details-title::before {
          content: '🔍';
          margin-right: 10px;
          font-size: 24px;
        }
        .detail-item {
          background: white;
          padding: 15px;
          margin: 10px 0;
          border-radius: 8px;
          border-left: 3px solid #d63384;
          box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .detail-label {
          font-weight: 600;
          color: #d63384;
          font-size: 13px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        .detail-value {
          font-size: 15px;
          color: #333;
          margin-top: 5px;
          word-break: break-all;
        }
        .footer {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          padding: 30px;
          text-align: center;
          border-top: 1px solid #dee2e6;
        }
        .timestamp {
          background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
          color: white;
          padding: 12px 20px;
          border-radius: 25px;
          display: inline-block;
          font-weight: 600;
          margin-bottom: 15px;
          box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }
        .footer-text {
          color: #6c757d;
          font-size: 14px;
          line-height: 1.5;
        }
        .brand {
          background: linear-gradient(135deg, #00457F 0%, #0066cc 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          font-weight: 700;
        }
        @media (max-width: 600px) {
          .email-container { margin: 10px; }
          .header, .content, .footer { padding: 20px; }
          .header h1 { font-size: 24px; }
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <div class="header">
          <h1>🛡️ Security Assessment Request</h1>
          <div class="subtitle">New request received from your platform</div>
        </div>

        <div class="content">
          <div class="info-grid">
            <div class="info-card">
              <div class="info-label">Organization</div>
              <div class="info-value">${data.organizationName}</div>
            </div>

            <div class="info-card">
              <div class="info-label">Project</div>
              <div class="info-value">${data.projectName}</div>
            </div>

            <div class="info-card">
              <div class="info-label">Assessment Type</div>
              <div class="info-value">${data.assessmentType}</div>
            </div>

            <div class="info-card">
              <div class="info-label">Description</div>
              <div class="info-value">${data.assessmentDescription}</div>
            </div>
          </div>

          <div class="details-section">
            <div class="details-title">Assessment Details</div>
            ${generateModernDetailsHTML(data.details)}
          </div>
        </div>

        <div class="footer">
          <div class="timestamp">📅 Submitted: ${data.submittedAt}</div>
          <div class="footer-text">
            This email was automatically generated by <span class="brand">Defendly Security Platform</span><br>
            Please review the assessment request and contact the organization for next steps.
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Generate modern HTML for assessment details with beautiful styling
 */
const generateModernDetailsHTML = (
  details: Record<string, string | number | undefined>
): string => {
  let html = "";

  for (const [key, value] of Object.entries(details)) {
    if (value && value !== "") {
      const label = key
        .replace(/([A-Z])/g, " $1")
        .replace(/^./, (str) => str.toUpperCase());

      // Special handling for file content
      if (key.includes('FileContent')) {
        const fileName = details[key.replace('Content', 'Name')] || 'uploaded-file';
        html += `
          <div class="detail-item">
            <div class="detail-label">${label}</div>
            <div class="detail-value">
              <strong>📎 File Attached: ${fileName}</strong><br>
              <small style="color: #666;">File content included as base64 data</small><br>
              <details style="margin-top: 10px;">
                <summary style="cursor: pointer; color: #00457F;">Click to view file content</summary>
                <div style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; margin-top: 5px; border-radius: 4px; font-family: monospace; font-size: 12px; word-break: break-all;">
                  ${value}
                </div>
              </details>
            </div>
          </div>
        `;
      } else if (key.includes('FileName')) {
        // Skip filename if we already handled file content
        if (!details[key.replace('Name', 'Content')]) {
          html += `
            <div class="detail-item">
              <div class="detail-label">${label}</div>
              <div class="detail-value">📎 ${value}</div>
            </div>
          `;
        }
      } else {
        html += `
          <div class="detail-item">
            <div class="detail-label">${label}</div>
            <div class="detail-value">${value}</div>
          </div>
        `;
      }
    }
  }

  return (
    html ||
    `
    <div class="detail-item">
      <div class="detail-label">Status</div>
      <div class="detail-value">No additional details provided</div>
    </div>
  `
  );
};

/**
 * Send notification email to user
 */
export const sendUserNotificationEmail = async (
  userEmail: string,
  assessmentType: string,
  organizationName: string
): Promise<boolean> => {
  try {
    const emailData: EmailData = {
      to: userEmail,
      subject: `Assessment Request Received - ${assessmentType}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Assessment Request Confirmation</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #00457F; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
            .content { background-color: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>✅ Assessment Request Confirmed</h1>
            </div>
            <div class="content">
              <p>Dear ${organizationName} Team,</p>
              <p>We have successfully received your <strong>${assessmentType}</strong> security assessment request.</p>
              <p>Our security experts will review your request and contact you within 24-48 hours with next steps.</p>
              <p>Thank you for choosing Defendly for your security assessment needs.</p>
              <br>
              <p>Best regards,<br>The Defendly Security Team</p>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    return await sendEmail(emailData);
  } catch (error) {
    console.error("Failed to send user notification email:", error);
    return false;
  }
};
