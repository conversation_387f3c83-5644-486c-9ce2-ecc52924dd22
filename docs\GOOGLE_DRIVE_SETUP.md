# 📁 Google Drive File Upload Setup

Get downloadable file links in your assessment emails!

## 🌟 Current Implementation

The system now supports **3 file upload methods** with automatic fallbacks:

### 1. **Google Drive** (Best - Permanent Links)
- ✅ Permanent download links
- ✅ Large file support
- ✅ Professional file sharing
- ⚙️ Requires Google API setup

### 2. **File.io** (Good - Temporary Links)  
- ✅ Works immediately (no setup)
- ✅ Clean download links
- ⚠️ Files deleted after 1 download
- ⚠️ 100MB file limit

### 3. **Local Storage** (Fallback - Demo)
- ✅ Works offline
- ✅ No external dependencies
- ⚠️ Files stored in browser only
- ⚠️ Lost on browser clear

## 🚀 Current Status (No Setup Required)

**Right now**, your file uploads work with these fallbacks:

1. **Try Google Drive** → Not configured, skip
2. **Try File.io** → ✅ **Works immediately!**
3. **Try Local Storage** → Fallback if needed

## 📧 Email Examples

### **JSON File Upload (API Testing):**
```
📎 File: api-specification.json
   Size: 15.2 KB
   Download: https://file.io/abc123xyz
```

### **APK File Upload (Mobile Testing):**
```
📎 File: my-app.apk
   Size: 25.4 MB
   Download: https://file.io/def456uvw
```

## 🧪 Test File Uploads Now

1. **Go to**: http://localhost:3001 → Assessment Center → Advanced Testing

2. **Test API Testing**:
   - Select "API Testing"
   - Upload a JSON file
   - Submit form
   - Check email for download link!

3. **Test Mobile App**:
   - Select "Mobile App"
   - Upload an APK file
   - Submit form
   - Check email for download link!

## 📋 Console Output

When you upload files, you'll see:
```
🚀 Starting file upload: api-spec.json (15.2 KB)
📁 Google Drive not available, trying file hosting service...
📁 File uploaded to file.io: api-spec.json
🔗 Download URL: https://file.io/abc123xyz
✅ API file uploaded: https://file.io/abc123xyz
```

## 🔧 Optional: Google Drive Setup (For Permanent Links)

If you want permanent download links instead of temporary ones:

### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project: "Defendly File Storage"
3. Enable Google Drive API

### Step 2: Create OAuth Credentials
1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. Application type: "Web application"
4. Authorized redirect URIs: `http://localhost:3001`
5. Copy Client ID and Client Secret

### Step 3: Update Environment Variables
Add to your `.env` file:
```env
# Existing variables
VITE_OPENAI_API_KEY=your_openai_key
VITE_API_BASE_URL=http://localhost:5000
VITE_WEB3FORMS_ACCESS_KEY=your_web3forms_key

# Add Google Drive variables
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_GOOGLE_CLIENT_SECRET=your_google_client_secret
VITE_GOOGLE_REDIRECT_URI=http://localhost:3001
```

### Step 4: Restart Server
```bash
npm run dev
```

## 🎯 File Upload Flow

```
User uploads file
       ↓
Try Google Drive (if configured)
       ↓ (fallback)
Try File.io (temporary links)
       ↓ (fallback)
Local storage (demo only)
       ↓
Include download link in email
```

## 🔍 Troubleshooting

### "File upload failed" message:
- Check internet connection
- Try smaller file size
- Check browser console for errors

### Download link not working:
- File.io links expire after 1 download
- Try uploading again for new link
- Consider Google Drive setup for permanent links

### Large files (>100MB):
- File.io has 100MB limit
- Use Google Drive for larger files
- Consider file compression

## 💡 Pro Tips

1. **File.io Links**: Work immediately but expire after 1 download
2. **File Names**: Keep them simple (no special characters)
3. **File Sizes**: Under 100MB work best with current setup
4. **Multiple Downloads**: Re-upload file for new download link

## 🎉 What's Working Now

✅ **File uploads with download links**
✅ **Automatic fallback system**
✅ **Email integration with download buttons**
✅ **Support for JSON, APK, and other file types**
✅ **File size and type detection**
✅ **Error handling and user feedback**

The system is ready to use! Test it by uploading files in the assessment form and checking your email for download links.
