// Simple file upload service using file.io for temporary downloadable links

/**
 * Upload file to file.io and return downloadable link
 * Files are automatically deleted after 1 download or 14 days
 */
export const uploadFile = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  try {
    console.log(`🚀 Uploading file: ${fileName || file.name} (${(file.size / 1024).toFixed(2)} KB)`);
    
    // Create form data for file.io
    const formData = new FormData();
    formData.append('file', file);

    // Upload to file.io
    const response = await fetch('https://file.io', {
      method: 'POST',
      body: formData,
      headers: {
        'Accept': 'application/json',
      }
    });

    console.log(`📡 File.io response status: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('📡 File.io response:', result);
      
      if (result.success && result.link) {
        console.log(`✅ File uploaded successfully: ${fileName || file.name}`);
        console.log(`🔗 Download URL: ${result.link}`);
        
        return {
          success: true,
          downloadUrl: result.link
        };
      } else {
        console.error('❌ File.io upload failed:', result);
        throw new Error(result.message || 'File.io upload failed');
      }
    } else {
      const errorText = await response.text();
      console.error('❌ File.io HTTP error:', response.status, errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
  } catch (error) {
    console.error('❌ File upload failed:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Upload failed' 
    };
  }
};

/**
 * Test the file upload service
 */
export const testFileUploadService = async (): Promise<void> => {
  console.log('🧪 Testing file.io upload service...');
  
  // Create a small test file
  const testContent = 'This is a test file for Defendly file upload service.';
  const testFile = new File([testContent], 'test.txt', { type: 'text/plain' });
  
  console.log('📤 Testing with file:', testFile.name, `(${testFile.size} bytes)`);
  
  // Test file.io
  const result = await uploadFile(testFile);
  if (result.success) {
    console.log('✅ File.io test successful:', result.downloadUrl);
  } else {
    console.log('❌ File.io test failed:', result.error);
  }
};
