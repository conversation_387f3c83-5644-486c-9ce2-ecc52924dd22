// Simple file upload service using file.io for temporary downloadable links

/**
 * Upload file to file.io and return downloadable link
 * Files are automatically deleted after 1 download or 14 days
 */
export const uploadFile = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  try {
    console.log(`🚀 Uploading file: ${fileName || file.name} (${(file.size / 1024).toFixed(2)} KB)`);
    
    // Create form data for file.io
    const formData = new FormData();
    formData.append('file', file);

    // Upload to file.io with CORS handling
    const response = await fetch('https://file.io', {
      method: 'POST',
      body: formData,
      mode: 'cors',
      headers: {
        'Accept': 'application/json',
      }
    });

    console.log(`📡 File.io response status: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('📡 File.io response:', result);
      
      if (result.success && result.link) {
        console.log(`✅ File uploaded successfully: ${fileName || file.name}`);
        console.log(`🔗 Download URL: ${result.link}`);
        
        return {
          success: true,
          downloadUrl: result.link
        };
      } else {
        console.error('❌ File.io upload failed:', result);
        throw new Error(result.message || 'File.io upload failed');
      }
    } else {
      const errorText = await response.text();
      console.error('❌ File.io HTTP error:', response.status, errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
  } catch (error) {
    console.error('❌ File upload failed:', error);

    // If file.io fails (likely CORS), try a simple fallback
    console.log('🔄 File.io failed, trying simple fallback...');
    return await createSimpleFileLink(file, fileName);
  }
};

/**
 * Create a simple file download using data URL (fallback method)
 */
const createSimpleFileLink = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  try {
    console.log('📝 Creating simple file link for:', fileName || file.name);

    // Convert file to base64 data URL
    const base64 = await fileToBase64(file);

    // Create a simple download link using data URL
    const downloadUrl = base64;

    console.log('✅ Simple file link created successfully');
    console.log('🔗 Download URL: data:// link (base64)');

    return {
      success: true,
      downloadUrl: downloadUrl
    };
  } catch (error) {
    console.error('❌ Simple file link creation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create file link'
    };
  }
};

/**
 * Convert file to base64 data URL
 */
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

/**
 * Test the file upload service with detailed debugging
 */
export const testFileUploadService = async (): Promise<void> => {
  console.log('🧪 Testing file.io upload service...');

  // First, test if file.io is accessible
  try {
    console.log('🌐 Testing file.io accessibility...');
    const testResponse = await fetch('https://file.io', {
      method: 'GET',
      mode: 'cors'
    });
    console.log('📡 File.io GET response:', testResponse.status, testResponse.statusText);
  } catch (error) {
    console.error('❌ File.io accessibility test failed:', error);
    console.log('🔍 This might be a CORS issue. Trying alternative approach...');
  }

  // Create a small test file
  const testContent = 'This is a test file for Defendly file upload service.';
  const testFile = new File([testContent], 'test.txt', { type: 'text/plain' });

  console.log('📤 Testing with file:', testFile.name, `(${testFile.size} bytes)`);

  // Test file.io upload
  const result = await uploadFile(testFile);
  if (result.success) {
    console.log('✅ File.io test successful:', result.downloadUrl);
  } else {
    console.log('❌ File.io test failed:', result.error);

    // Try alternative approach without CORS
    console.log('🔄 Trying alternative upload method...');
    await testAlternativeUpload(testFile);
  }
};

/**
 * Test alternative upload method using a different approach
 */
const testAlternativeUpload = async (file: File): Promise<void> => {
  try {
    console.log('🧪 Testing alternative upload approach...');

    // Try using a different file hosting service that might have better CORS support
    const formData = new FormData();
    formData.append('file', file);

    // Try 0x0.st which usually has better CORS support
    const response = await fetch('https://0x0.st', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const downloadUrl = await response.text();
      console.log('✅ Alternative upload successful:', downloadUrl.trim());
      return;
    }

    throw new Error('Alternative upload failed');
  } catch (error) {
    console.error('❌ Alternative upload failed:', error);
    console.log('💡 Suggestion: File upload services might be blocked. Consider using a backend proxy.');
  }
};
