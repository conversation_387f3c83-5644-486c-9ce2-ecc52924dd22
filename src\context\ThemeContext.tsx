// src/context/ThemeContext.tsx
import { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

const ThemeContext = createContext<{
  theme: Theme;
  toggleTheme: () => void;
}>({
  theme: 'dark', // Updated default value here for type-safety, though it won't be used
  toggleTheme: () => {},
});

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [theme, setTheme] = useState<Theme>(() => {
    const stored = localStorage.getItem('theme');
    if (stored) return stored as Theme;
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    if (stored) return stored as Theme;
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  });

  // Initialize CSS variables immediately
  useEffect(() => {
    const root = document.documentElement;
    if (theme === 'dark') {
      root.style.setProperty('--sidebar-bg', '#202020');
      root.style.setProperty('--sidebar-border', '#232323');
      root.style.setProperty('--sidebar-text', '#d1d5db');
      root.style.setProperty('--sidebar-hover', '#374151');
    } else {
      root.style.setProperty('--sidebar-bg', '#ffffff');
      root.style.setProperty('--sidebar-border', '#e5e7eb');
      root.style.setProperty('--sidebar-text', '#4b5563');
      root.style.setProperty('--sidebar-hover', '#f3f4f6');
    }
  }, []);
  useEffect(() => {
    const root = document.documentElement;

    // Force immediate class update
    root.classList.remove('light', 'dark');
    root.classList.add(theme);

    // Set CSS variables for immediate theme switching
    if (theme === 'dark') {
      root.style.setProperty('--sidebar-bg', '#202020');
      root.style.setProperty('--sidebar-border', '#232323');
      root.style.setProperty('--sidebar-text', '#d1d5db');
      root.style.setProperty('--sidebar-hover', '#374151');
    } else {
      root.style.setProperty('--sidebar-bg', '#ffffff');
      root.style.setProperty('--sidebar-border', '#e5e7eb');
      root.style.setProperty('--sidebar-text', '#4b5563');
      root.style.setProperty('--sidebar-hover', '#f3f4f6');
    }

    // Store in localStorage
    localStorage.setItem('theme', theme);
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prev => (prev === 'light' ? 'dark' : 'light'));
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext);
